<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>给高雅楠的表白</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(45deg, #ff6b9d, #c44569, #f8b500, #ff6b9d);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            text-align: center;
            color: white;
            z-index: 10;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .title {
            font-size: 3.5rem;
            margin-bottom: 2rem;
            animation: fadeInDown 2s ease-out;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
        }

        .name {
            color: #ffeb3b;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
            font-weight: bold;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 2px 2px 8px rgba(0,0,0,0.5), 0 0 20px #ffeb3b; }
            to { text-shadow: 2px 2px 8px rgba(0,0,0,0.5), 0 0 30px #ffeb3b, 0 0 40px #ffeb3b; }
        }

        .message {
            font-size: 1.6rem;
            line-height: 1.8;
            margin-bottom: 3rem;
            animation: fadeInUp 2s ease-out 0.5s both;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            text-shadow: 1px 1px 4px rgba(0,0,0,0.5);
        }

        .heart {
            font-size: 5rem;
            color: #ff6b9d;
            animation: heartbeat 1.2s ease-in-out infinite;
            margin: 2rem 0;
            filter: drop-shadow(0 0 20px #ff6b9d);
        }

        .dancing-hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .dancing-heart {
            position: absolute;
            font-size: 2rem;
            color: #ff6b9d;
            animation: dance 3s ease-in-out infinite;
            filter: drop-shadow(0 0 10px #ff6b9d);
        }

        @keyframes dance {
            0%, 100% { transform: translateY(0) rotate(0deg) scale(1); }
            25% { transform: translateY(-20px) rotate(5deg) scale(1.1); }
            50% { transform: translateY(-10px) rotate(-5deg) scale(0.9); }
            75% { transform: translateY(-30px) rotate(3deg) scale(1.2); }
        }

        .button {
            background: linear-gradient(45deg, #ff6b9d, #ff8e8e);
            border: none;
            padding: 15px 30px;
            font-size: 1.2rem;
            color: white;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: fadeInUp 2s ease-out 1s both;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
            margin: 10px;
        }

        .button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.6);
        }

        .button.reject {
            background: linear-gradient(45deg, #666, #888);
        }

        .button.reject:hover {
            box-shadow: 0 6px 20px rgba(102, 102, 102, 0.6);
        }

        .floating-hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-heart {
            position: absolute;
            color: rgba(255, 107, 157, 0.7);
            font-size: 2rem;
            animation: float 6s linear infinite;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes heartbeat {
            0%, 100% {
                transform: scale(1);
            }
            25% {
                transform: scale(1.3) rotate(5deg);
            }
            50% {
                transform: scale(1.5) rotate(-5deg);
            }
            75% {
                transform: scale(1.2) rotate(3deg);
            }
        }

        .pulse-heart {
            position: fixed;
            font-size: 3rem;
            color: #ff6b9d;
            animation: pulse 2s ease-in-out infinite;
            pointer-events: none;
            z-index: 8;
            filter: drop-shadow(0 0 15px #ff6b9d);
        }

        @keyframes pulse {
            0% { transform: scale(0.8); opacity: 0.7; }
            50% { transform: scale(1.2); opacity: 1; }
            100% { transform: scale(0.8); opacity: 0.7; }
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .stars {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .star {
            position: absolute;
            color: rgba(255, 255, 255, 0.8);
            animation: twinkle 2s infinite;
        }

        @keyframes twinkle {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        .response {
            margin-top: 2rem;
            font-size: 1.3rem;
            opacity: 0;
            transition: opacity 1s ease;
        }

        .response.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="stars" id="stars"></div>
    <div class="floating-hearts" id="floating-hearts"></div>
    <div class="dancing-hearts" id="dancing-hearts"></div>

    <div class="container">
        <h1 class="title"><span class="name">高雅楠</span></h1>

        <div class="message">

            我想对你说：<br>
            <strong>💖 我喜欢你，愿意做我的女朋友吗？💖</strong>
        </div>

        <div class="heart">💖</div>
        <div class="heart">💕</div>
        <div class="heart">💗</div>

        <div class="response" id="response" style="display: none;">
            <div class="heart">💕💕💕</div>
            <h2 style="color: #ffeb3b; font-size: 2.5rem; margin: 20px 0;">🎉 太好了！叫主人！🎉</h2>
            <p style="font-size: 1.4rem;">谢谢你接受！<br>我会好好珍惜你的！<br>💖💖💖</p>
        </div>
    </div>

    <script>
        // 创建飘浮的心形
        function createFloatingHeart() {
            const heart = document.createElement('div');
            heart.className = 'floating-heart';
            heart.innerHTML = '💖';
            heart.style.left = Math.random() * 100 + '%';
            heart.style.animationDelay = Math.random() * 2 + 's';
            heart.style.animationDuration = (Math.random() * 3 + 4) + 's';
            document.getElementById('floating-hearts').appendChild(heart);

            setTimeout(() => {
                heart.remove();
            }, 7000);
        }

        // 创建星星
        function createStars() {
            const starsContainer = document.getElementById('stars');
            for (let i = 0; i < 50; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.innerHTML = '✨';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 2 + 's';
                star.style.fontSize = (Math.random() * 10 + 10) + 'px';
                starsContainer.appendChild(star);
            }
        }

        // 拒绝的理由数组
        const rejectReasons = [
            "你再想想？",
            "不要这么快拒绝嘛~",
            "给我一个机会好吗？",
            "我会对你很好的！",
            "我们很合适的！",
            "你真的不考虑一下吗？",
            "我可以等你的！",
            "我们可以先做朋友！",
            "我真的很喜欢你！",
            "求求你了~",
            "我会改变的！",
            "给我们一个开始的机会！",
            "我保证会让你幸福的！",
            "你就答应我吧！",
            "我不会放弃的！"
        ];

        let rejectCount = 0;

        // 创建跳动的爱心
        function createDancingHearts() {
            const dancingContainer = document.getElementById('dancing-hearts');
            for (let i = 0; i < 15; i++) {
                const heart = document.createElement('div');
                heart.className = 'dancing-heart';
                heart.innerHTML = ['💖', '💕', '💗', '💝', '💘'][Math.floor(Math.random() * 5)];
                heart.style.left = Math.random() * 100 + '%';
                heart.style.top = Math.random() * 100 + '%';
                heart.style.animationDelay = Math.random() * 3 + 's';
                heart.style.animationDuration = (Math.random() * 2 + 2) + 's';
                dancingContainer.appendChild(heart);
            }
        }

        // 创建脉冲爱心
        function createPulseHearts() {
            for (let i = 0; i < 8; i++) {
                const heart = document.createElement('div');
                heart.className = 'pulse-heart';
                heart.innerHTML = '💖';
                heart.style.left = Math.random() * window.innerWidth + 'px';
                heart.style.top = Math.random() * window.innerHeight + 'px';
                heart.style.animationDelay = Math.random() * 2 + 's';
                document.body.appendChild(heart);
            }
        }

        // 开始表白（页面加载后自动触发）
        function startConfession() {
            // 延迟1秒后自动弹出表白
            setTimeout(() => {
                askForAnswer();
            }, 1000);
        }

        // 询问答案
        function askForAnswer() {
            const result = confirm("高雅楠，你愿意做我的小狗吗？\n\n点击'确定'表示愿意\n点击'取消'表示拒绝");

            if (result) {
                // 接受表白
                showSuccess();
            } else {
                // 拒绝表白，继续询问
                handleReject();
            }
        }

        // 处理拒绝
        function handleReject() {
            rejectCount++;

            if (rejectCount < rejectReasons.length) {
                const reason = rejectReasons[rejectCount - 1];
                setTimeout(() => {
                    alert(reason);
                    askForAnswer();
                }, 500);
            } else {
                // 所有理由都用完了，最后一次请求
                setTimeout(() => {
                    alert("我真的真的很喜欢你！最后一次机会！");
                    const finalResult = confirm("求求你了，答应我好吗？💖");
                    if (finalResult) {
                        showSuccess();
                    } else {
                        // 强制成功
                        alert("不管怎样，我都要告诉你：我们在一起了！💕");
                        showSuccess();
                    }
                }, 500);
            }
        }

        // 显示成功
        function showSuccess() {
            document.getElementById('response').style.display = 'block';

            // 播放庆祝动画
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    createCelebrationHeart();
                }, i * 200);
            }

            // 显示最终消息
            setTimeout(() => {
                alert("太好了！叫主人！💖💖💖");
            }, 1000);
        }

        // 创建庆祝爱心
        function createCelebrationHeart() {
            const heart = document.createElement('div');
            heart.style.position = 'fixed';
            heart.style.fontSize = '3rem';
            heart.style.color = '#ff6b9d';
            heart.style.left = Math.random() * window.innerWidth + 'px';
            heart.style.top = window.innerHeight + 'px';
            heart.style.pointerEvents = 'none';
            heart.style.zIndex = '1000';
            heart.innerHTML = '💖';
            document.body.appendChild(heart);

            // 动画上升
            let pos = window.innerHeight;
            const interval = setInterval(() => {
                pos -= 5;
                heart.style.top = pos + 'px';
                if (pos < -100) {
                    clearInterval(interval);
                    heart.remove();
                }
            }, 50);
        }

        // 初始化
        window.addEventListener('load', function() {
            createStars();
            createDancingHearts();
            createPulseHearts();
            setInterval(createFloatingHeart, 600);

            // 页面加载完成后自动开始表白
            startConfession();
        });
    </script>
</body>
</html>
