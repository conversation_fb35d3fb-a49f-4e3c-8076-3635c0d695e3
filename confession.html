<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>给高雅楠的表白</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .container {
            text-align: center;
            color: white;
            z-index: 10;
            position: relative;
        }

        .title {
            font-size: 3rem;
            margin-bottom: 2rem;
            animation: fadeInDown 2s ease-out;
        }

        .name {
            color: #ff6b9d;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: bold;
        }

        .message {
            font-size: 1.5rem;
            line-height: 1.8;
            margin-bottom: 3rem;
            animation: fadeInUp 2s ease-out 0.5s both;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .heart {
            font-size: 4rem;
            color: #ff6b9d;
            animation: heartbeat 1.5s ease-in-out infinite;
            margin: 2rem 0;
        }

        .button {
            background: linear-gradient(45deg, #ff6b9d, #ff8e8e);
            border: none;
            padding: 15px 30px;
            font-size: 1.2rem;
            color: white;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: fadeInUp 2s ease-out 1s both;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
        }

        .button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.6);
        }

        .floating-hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-heart {
            position: absolute;
            color: rgba(255, 107, 157, 0.7);
            font-size: 2rem;
            animation: float 6s linear infinite;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes heartbeat {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.2);
            }
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .stars {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .star {
            position: absolute;
            color: rgba(255, 255, 255, 0.8);
            animation: twinkle 2s infinite;
        }

        @keyframes twinkle {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        .response {
            margin-top: 2rem;
            font-size: 1.3rem;
            opacity: 0;
            transition: opacity 1s ease;
        }

        .response.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="stars" id="stars"></div>
    <div class="floating-hearts" id="floating-hearts"></div>
    
    <div class="container">
        <h1 class="title">亲爱的<span class="name">高雅楠</span></h1>
        
        <div class="message">
            从遇见你的那一刻起，<br>
            我的世界就变得不一样了。<br>
            你的笑容如春日暖阳，<br>
            照亮了我心中的每一个角落。<br><br>
            
            我想对你说：<br>
            <strong>我喜欢你，愿意和你一起走过人生的每一个季节。</strong>
        </div>
        
        <div class="heart">💖</div>
        
        <button class="button" onclick="showResponse()">点击查看我的心意</button>
        
        <div class="response" id="response">
            <div class="heart">💕</div>
            <p>无论你的答案是什么，<br>我都会珍惜这份美好的感情。<br>愿你永远快乐！</p>
        </div>
    </div>

    <script>
        // 创建飘浮的心形
        function createFloatingHeart() {
            const heart = document.createElement('div');
            heart.className = 'floating-heart';
            heart.innerHTML = '💖';
            heart.style.left = Math.random() * 100 + '%';
            heart.style.animationDelay = Math.random() * 2 + 's';
            heart.style.animationDuration = (Math.random() * 3 + 4) + 's';
            document.getElementById('floating-hearts').appendChild(heart);
            
            setTimeout(() => {
                heart.remove();
            }, 7000);
        }

        // 创建星星
        function createStars() {
            const starsContainer = document.getElementById('stars');
            for (let i = 0; i < 50; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.innerHTML = '✨';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 2 + 's';
                star.style.fontSize = (Math.random() * 10 + 10) + 'px';
                starsContainer.appendChild(star);
            }
        }

        // 显示回应
        function showResponse() {
            document.getElementById('response').classList.add('show');
        }

        // 初始化
        createStars();
        setInterval(createFloatingHeart, 800);
    </script>
</body>
</html>
