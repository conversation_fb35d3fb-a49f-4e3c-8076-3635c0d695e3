<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>给高雅楠的表白</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #ffe0f0 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .container {
            text-align: center;
            color: #333;
            z-index: 10;
            position: relative;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 50px;
            box-shadow: 0 10px 30px rgba(255, 107, 157, 0.2);
            max-width: 600px;
        }

        .title {
            font-size: 2.8rem;
            margin-bottom: 2rem;
            animation: fadeInDown 1.5s ease-out;
            color: #333;
        }

        .name {
            color: #ff6b9d;
            font-weight: bold;
        }

        .message {
            font-size: 1.4rem;
            line-height: 1.8;
            margin-bottom: 2rem;
            animation: fadeInUp 1.5s ease-out 0.3s both;
            color: #555;
        }

        .heart {
            font-size: 3rem;
            color: #ff6b9d;
            animation: heartbeat 1.5s ease-in-out infinite;
            margin: 1rem 0;
        }

        .button {
            background: linear-gradient(45deg, #ff6b9d, #ff8e8e);
            border: none;
            padding: 15px 30px;
            font-size: 1.2rem;
            color: white;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: fadeInUp 2s ease-out 1s both;
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
            margin: 10px;
        }

        .button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 107, 157, 0.6);
        }

        .button.reject {
            background: linear-gradient(45deg, #666, #888);
        }

        .button.reject:hover {
            box-shadow: 0 6px 20px rgba(102, 102, 102, 0.6);
        }

        .floating-hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-heart {
            position: absolute;
            color: #ff6b9d;
            font-size: 1.5rem;
            animation: float 8s linear infinite;
            opacity: 0.8;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes heartbeat {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }



        .response {
            margin-top: 2rem;
            font-size: 1.3rem;
            opacity: 0;
            transition: opacity 1s ease;
        }

        .response.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="floating-hearts" id="floating-hearts"></div>

    <div class="container">
        <h1 class="title">亲爱的<span class="name">高雅楠</span></h1>

        <div class="message">
            从遇见你的那一刻起，<br>
            我的世界就变得不一样了。<br>
            你的笑容如春日暖阳，<br>
            照亮了我心中的每一个角落。<br><br>

            我想对你说：<br>
            <strong>我喜欢你，愿意做我的女朋友吗？</strong>
        </div>

        <div class="heart">�</div>

        <div class="response" id="response" style="display: none;">
            <div class="heart">�</div>
            <h2 style="color: #ff6b9d; font-size: 2rem; margin: 20px 0;">太好了！我们在一起了！</h2>
            <p style="font-size: 1.2rem; color: #555;">谢谢你接受我的表白！<br>我会好好珍惜你的！💖</p>
        </div>
    </div>

    <script>
        // 创建飘浮的心形
        function createFloatingHeart() {
            const heart = document.createElement('div');
            heart.className = 'floating-heart';
            heart.innerHTML = '💖';
            heart.style.left = Math.random() * 100 + '%';
            heart.style.animationDelay = Math.random() * 2 + 's';
            heart.style.animationDuration = (Math.random() * 3 + 4) + 's';
            document.getElementById('floating-hearts').appendChild(heart);

            setTimeout(() => {
                heart.remove();
            }, 7000);
        }



        // 拒绝的理由数组
        const rejectReasons = [
            "你再想想？",
            "不要这么快拒绝嘛~",
            "给我一个机会好吗？",
            "我会对你很好的！",
            "我们很合适的！",
            "你真的不考虑一下吗？",
            "我可以等你的！",
            "我们可以先做朋友！",
            "我真的很喜欢你！",
            "求求你了~",
            "我会改变的！",
            "给我们一个开始的机会！",
            "我保证会让你幸福的！",
            "你就答应我吧！",
            "我不会放弃的！"
        ];

        let rejectCount = 0;



        // 开始表白（页面加载后自动触发）
        function startConfession() {
            // 延迟1秒后自动弹出表白
            setTimeout(() => {
                askForAnswer();
            }, 1000);
        }

        // 询问答案
        function askForAnswer() {
            const result = confirm("高雅楠，你愿意做我的女朋友吗？\n\n点击'确定'表示愿意\n点击'取消'表示拒绝");

            if (result) {
                // 接受表白
                showSuccess();
            } else {
                // 拒绝表白，继续询问
                handleReject();
            }
        }

        // 处理拒绝
        function handleReject() {
            rejectCount++;

            if (rejectCount < rejectReasons.length) {
                const reason = rejectReasons[rejectCount - 1];
                setTimeout(() => {
                    alert(reason);
                    askForAnswer();
                }, 500);
            } else {
                // 所有理由都用完了，最后一次请求
                setTimeout(() => {
                    alert("我真的真的很喜欢你！最后一次机会！");
                    const finalResult = confirm("求求你了，答应我好吗？💖");
                    if (finalResult) {
                        showSuccess();
                    } else {
                        // 强制成功
                        alert("不管怎样，我都要告诉你：我们在一起了！💕");
                        showSuccess();
                    }
                }, 500);
            }
        }

        // 显示成功
        function showSuccess() {
            document.getElementById('response').style.display = 'block';

            // 播放庆祝动画
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    createCelebrationHeart();
                }, i * 200);
            }

            // 显示最终消息
            setTimeout(() => {
                alert("太好了！我们在一起了！💖💖💖");
            }, 1000);
        }

        // 创建庆祝爱心
        function createCelebrationHeart() {
            const heart = document.createElement('div');
            heart.style.position = 'fixed';
            heart.style.fontSize = '3rem';
            heart.style.color = '#ff6b9d';
            heart.style.left = Math.random() * window.innerWidth + 'px';
            heart.style.top = window.innerHeight + 'px';
            heart.style.pointerEvents = 'none';
            heart.style.zIndex = '1000';
            heart.innerHTML = '💖';
            document.body.appendChild(heart);

            // 动画上升
            let pos = window.innerHeight;
            const interval = setInterval(() => {
                pos -= 5;
                heart.style.top = pos + 'px';
                if (pos < -100) {
                    clearInterval(interval);
                    heart.remove();
                }
            }, 50);
        }

        // 初始化
        window.addEventListener('load', function() {
            setInterval(createFloatingHeart, 1200);

            // 页面加载完成后自动开始表白
            startConfession();
        });
    </script>
</body>
</html>
